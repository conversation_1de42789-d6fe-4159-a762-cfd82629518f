"""
播放器服务模块
负责视频播放和字幕显示
"""
import platform
import subprocess
import shutil
import logging
from pathlib import Path
from typing import Optional
import os
import time # 确保 time 被导入

from ..core.exceptions import PlayerError
from ..config.settings import settings # 假设 settings 中有 MPV_PATH

logger = logging.getLogger(__name__)


class PlayerService:
    """播放器服务类（基于MPV命令行）"""

    def __init__(self, mpv_path: Optional[str] = None):
        # 如果 mpv_path 未显式提供，则从 settings 中获取
        # 如果 settings 中也没有，则 shutil.which 会尝试从 PATH 中查找 "mpv"
        self.mpv_executable = mpv_path or settings.MPV_PATH
        self._check_mpv_availability()
        logger.info(f"PlayerService initialized with MPV path: {self.mpv_executable}")

    def _check_mpv_availability(self):
        """检查MPV是否可用"""
        resolved_path = shutil.which(self.mpv_executable)
        if not resolved_path:
            raise PlayerError(
                f"MPV not found. Tried '{self.mpv_executable}'. "
                f"Please ensure MPV is installed and in your system PATH, "
                f"or configure the correct path in settings."
            )
        # 更新为shutil.which找到的绝对路径，以增加可靠性
        self.mpv_executable = resolved_path


    def play_video_with_subtitles(self, video_path: str, subtitle_path: Optional[str] = None):
        """
        使用MPV播放视频并加载字幕。
        """
        video_file = Path(video_path)
        if not video_file.exists():
            logger.error(f"Video file not found: {video_path}")
            raise PlayerError(f"Video file not found: {video_path}")

        command = [
            self.mpv_executable,
            str(video_file),
            "--force-window=yes",
            "--keep-open=yes",
            "--msg-level=all=v" # 请求详细日志，以便调试
            # "--vo=gpu", # 可以尝试明确指定，如果默认选择有问题
            # "--no-config", # 忽略用户配置文件进行干净测试
        ]

        if subtitle_path:
            subtitle_file = Path(subtitle_path)
            if subtitle_file.exists():
                command.extend([
                    f"--sub-file={str(subtitle_file)}",
                    "--sub-auto=fuzzy",
                    "--sub-font-size=48", # 可以根据需要调整
                ])
                logger.info(f"Attempting to load subtitles: {subtitle_file}")
            else:
                logger.warning(f"Subtitle file not found, will play video without subtitles: {subtitle_file}")
        else:
            logger.info("No subtitle file provided, playing video without external subtitles.")

        logger.info(f"Executing MPV command: {' '.join(command)}")

        # 尝试创建一个更干净的环境变量副本传递给子进程
        env_for_mpv = os.environ.copy()
        dylp_modified = False
        if "DYLD_LIBRARY_PATH" in env_for_mpv:
            original_dylp = env_for_mpv["DYLD_LIBRARY_PATH"]
            # 尝试只保留不包含 anaconda 或 miniforge 的路径，并加上 homebrew (如果需要)
            # 或者直接删除它，让 MPV 依赖 rpath 和系统默认路径
            # logger.info(f"Original DYLD_LIBRARY_PATH for MPV subprocess: {original_dylp}")
            del env_for_mpv["DYLD_LIBRARY_PATH"]
            dylp_modified = True
            logger.info("Removed DYLD_LIBRARY_PATH for MPV subprocess.")

        if "DYLD_FALLBACK_LIBRARY_PATH" in env_for_mpv:
            # logger.info(f"Original DYLD_FALLBACK_LIBRARY_PATH for MPV subprocess: {env_for_mpv['DYLD_FALLBACK_LIBRARY_PATH']}")
            del env_for_mpv["DYLD_FALLBACK_LIBRARY_PATH"]
            dylp_modified = True
            logger.info("Removed DYLD_FALLBACK_LIBRARY_PATH for MPV subprocess.")

        if not dylp_modified:
            logger.info("No DYLD_LIBRARY_PATH or DYLD_FALLBACK_LIBRARY_PATH found in current environment to remove for MPV subprocess, or they were already clean.")

        try:
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env_for_mpv # 使用修改后的环境变量
            )

            time.sleep(1.5) # 稍微增加等待时间，给MPV更多启动时间
            if process.poll() is not None:
                # 如果进程立即结束，获取输出
                stdout, stderr = process.communicate(timeout=5) # 设置超时
                error_message = f"MPV failed to start or exited immediately (return code: {process.returncode})."
                if stdout:
                    error_message += f"\nMPV STDOUT:\n{stdout}"
                if stderr:
                    error_message += f"\nMPV STDERR:\n{stderr}"
                logger.error(error_message)
                raise PlayerError(error_message)
            else:
                logger.info(f"MPV started successfully (PID: {process.pid}). Playback should be in a separate window.")
                # 注意：Popen 是非阻塞的，MPV会在后台运行。
                # 在GUI应用中，这是期望的。如果需要等待MPV结束，可以使用 process.wait() 或 process.communicate()
                # 但这会阻塞当前线程，对于GUI的外部播放器调用，通常不需要等待。

        except FileNotFoundError:
            err_msg = f"MPV executable not found at '{self.mpv_executable}'. Please check installation and PATH."
            logger.error(err_msg)
            raise PlayerError(err_msg)
        except subprocess.TimeoutExpired: # communicate 超时
            process.kill()
            stdout, stderr = process.communicate()
            err_msg = f"MPV process timed out during communicate."
            if stdout: err_msg += f"\nMPV STDOUT (before timeout):\n{stdout}"
            if stderr: err_msg += f"\nMPV STDERR (before timeout):\n{stderr}"
            logger.error(err_msg)
            raise PlayerError(err_msg)
        except Exception as e:
            err_msg = f"Error during MPV playback: {str(e)}"
            logger.error(err_msg)
            # 尝试捕获子进程的输出，即使发生其他异常
            if 'process' in locals() and hasattr(process, 'communicate'):
                try:
                    stdout, stderr = process.communicate(timeout=1)
                    if stdout: err_msg += f"\nMPV STDOUT (on exception):\n{stdout}"
                    if stderr: err_msg += f"\nMPV STDERR (on exception):\n{stderr}"
                except:
                    pass # 忽略 communicate 中的错误
            raise PlayerError(err_msg)


# --- MPVWidget 类保持不变，因为它是为内嵌GUI预留的 ---
class MPVWidget:
    """MPV播放器组件（为GUI集成预留）"""
    def __init__(self):
        self.mpv_process = None
        self.is_playing = False
        self.current_position = 0.0
        self.duration = 0.0
    def load_video(self, video_path: str): pass
    def load_subtitles(self, subtitle_path: str): pass
    def play(self): pass
    def pause(self): pass
    def stop(self): pass
    def seek(self, position: float): pass
    def get_position(self) -> float: return self.current_position
    def get_duration(self) -> float: return self.duration